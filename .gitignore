# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/

# OS
Thumbs.db

# docs
.docs/

# Package manager lock files (optional - uncomment if needed)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
