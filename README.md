# نظام إدارة الزكاة - Zakat Management System

A **demo web application** for showcasing UI/UX design for managing Zakat (Islamic charity) requests and distributions. This is a frontend-focused demonstration using mock data to showcase the user interface and user experience.

## 🌟 Features

- **Demo Application**: Uses mock data for UI/UX demonstration
- **Multi-language Support**: Arabic (RTL) and English interface
- **User Authentication**: Demo login system with predefined users
- **Dashboard**: Comprehensive overview with mock statistics and data
- **Request Management**: Interactive forms and request tracking (demo data)
- **Reports & Analytics**: Beautiful charts and visualizations with sample data
- **Profile Management**: User profile interface demonstration
- **Task Management**: Administrative task interface with mock tasks
- **Responsive Design**: Mobile-friendly interface with dark/light theme support

## 🛠️ Technologies Used

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Radix UI Components
- **Authentication**: NextAuth.js (demo mode with mock data)
- **Data**: Mock data stored in TypeScript files (no database)
- **Charts**: Chart.js, Recharts, Plotly.js
- **Forms**: React Hook Form with Zod validation
- **Internationalization**: i18next
- **State Management**: Zustand, Jotai
- **UI Components**: Radix UI, Lucide React Icons

## 📋 Prerequisites

Before running this application, make sure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** or **yarn** package manager

**Note**: No database is required as this is a demo application using mock data.

## 🚀 Installation & Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd zakat_management_system/app
```

### 2. Install Dependencies

```bash
npm install --legacy-peer-deps
```

Note: We use `--legacy-peer-deps` due to ESLint version conflicts.

### 3. Environment Configuration

The `.env` file is already configured for demo purposes:

```env
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="GPnD51OEWoenNLLfX8zLRWByTn1mGGts"

# Note: This is a demo application using mock data
# No database is required - all data is stored in lib/mock-data.ts
```

### 4. Start the Development Server

```bash
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
app/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Dashboard pages
│   ├── profile/           # Profile management
│   ├── reports/           # Reports and analytics
│   ├── requests/          # Zakat request management
│   ├── signup/            # User registration
│   ├── tasks/             # Task management
│   └── test/              # Test pages
├── components/            # Reusable UI components
│   ├── dashboard/         # Dashboard-specific components
│   ├── layout/            # Layout components
│   └── ui/                # Base UI components
├── hooks/                 # Custom React hooks
├── lib/                   # Utility functions and configurations
├── prisma/                # Database schema and migrations
├── providers/             # React context providers
└── types/                 # TypeScript type definitions
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🌐 Usage

1. **Access the Application**: Navigate to http://localhost:3000
2. **Authentication**: The app redirects to `/auth/login` by default
3. **Demo Login**: Use the predefined demo account:
   - **Email**: `<EMAIL>`
   - **Password**: `johndoe123`
4. **Explore Features**:
   - **Dashboard**: View mock statistics and data visualizations
   - **Requests**: Browse sample Zakat requests and submit new ones (demo)
   - **Profile**: View and edit user profile interface
   - **Reports**: Explore charts and analytics with sample data
   - **Tasks**: Administrative task management interface

## 📊 Demo Data

This application uses mock data located in `lib/mock-data.ts` including:

- **Users**: Different user roles (admin, manager, applicant, etc.)
- **Zakat Requests**: Sample assistance requests with various statuses
- **Tasks**: Administrative tasks for different user roles
- **Statistics**: Dashboard metrics and analytics data
- **Reports**: Sample data for charts and visualizations

### Demo Login Credentials

- **Admin**: `<EMAIL>` / `johndoe123`
- **Other users**: Available in `lib/mock-data.ts`

## 🗑️ What Can Be Removed for Production

If you want to convert this to a real application, you can remove:

1. **Prisma-related files** (currently unused):
   - `prisma/schema.prisma`
   - `lib/db.ts`
   - Prisma dependencies in `package.json`

2. **Mock data system**:
   - `lib/mock-data.ts`
   - Replace with real API calls and database integration

3. **Demo authentication**:
   - Update `app/api/auth/[...nextauth]/route.ts` for real authentication
   - Remove hardcoded password checks

## 🔒 Security Features (Demo)

- Demo authentication with NextAuth.js
- JWT token management
- Environment variable protection
- CSRF protection (NextAuth built-in)

## 🌍 Internationalization

The application supports:
- Arabic (العربية) - RTL layout
- English - LTR layout

Language detection is automatic based on browser settings.

## 🎨 UI/UX Features

- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark/Light Theme**: Toggle between themes
- **RTL Support**: Full right-to-left layout for Arabic
- **Accessibility**: ARIA labels and keyboard navigation
- **Modern UI**: Clean, professional interface with smooth animations

## 🐛 Troubleshooting

### Common Issues

1. **Dependency Conflicts**: Use `npm install --legacy-peer-deps`
2. **Database Connection**: Ensure PostgreSQL is running and DATABASE_URL is correct
3. **Port Already in Use**: Change the port in next.config.js or kill the process using port 3000

### Development Tips

- Check browser console for client-side errors
- Monitor terminal output for server-side issues
- Ensure all environment variables are properly set

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🤝 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Built with ❤️ for the Muslim community**
