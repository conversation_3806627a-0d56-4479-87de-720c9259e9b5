'use client'

import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AssistanceTypeForm } from '@/components/admin/assistance-type-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { mockAssistanceTypes } from '@/lib/mock-data'
import type { RequiredDocument } from '@/lib/types'

export default function EditAssistanceTypePage() {
  const params = useParams()
  const router = useRouter()
  const { t } = useTranslation()
  const { toast } = useToast()
  const typeId = params.id as string

  const assistanceType = mockAssistanceTypes.find(type => type.id === typeId)

  if (!assistanceType) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Assistance Type Not Found</h2>
            <p className="text-muted-foreground mb-4">The requested assistance type could not be found.</p>
            <Button onClick={() => router.push('/admin/assistance-types')}>
              Back to Assistance Types
            </Button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  const handleSubmit = async (data: {
    nameAr: string
    nameEn: string
    descriptionAr: string
    descriptionEn: string
    maxAmount: number
    category: string
    isActive: boolean
    requiredDocuments: RequiredDocument[]
  }) => {
    try {
      // In a real app, this would be an API call
      console.log('Updating assistance type:', typeId, data)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'Success',
        description: 'Assistance type updated successfully',
      })
      
      router.push(`/admin/assistance-types/${typeId}`)
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update assistance type',
        variant: 'destructive',
      })
    }
  }

  const handleCancel = () => {
    router.push(`/admin/assistance-types/${typeId}`)
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Details
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Edit Assistance Type
            </h1>
            <p className="text-muted-foreground">
              Modify the configuration of this assistance type
            </p>
          </div>
        </div>

        {/* Form */}
        <AssistanceTypeForm
          initialData={assistanceType}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </AdminLayout>
  )
}
