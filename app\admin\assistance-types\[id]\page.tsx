'use client'

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Power, 
  PowerOff,
  FileText,
  Users,
  TrendingUp
} from 'lucide-react'
import { mockAssistanceTypes } from '@/lib/mock-data'

export default function AssistanceTypeDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { t, i18n } = useTranslation()
  const typeId = params.id as string

  const assistanceType = mockAssistanceTypes.find(type => type.id === typeId)

  if (!assistanceType) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Assistance Type Not Found</h2>
            <p className="text-muted-foreground mb-4">The requested assistance type could not be found.</p>
            <Button onClick={() => router.push('/admin/assistance-types')}>
              Back to Assistance Types
            </Button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.push('/admin/assistance-types')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Assistance Types
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {i18n.language === 'ar' ? assistanceType.nameAr : assistanceType.nameEn}
              </h1>
              <p className="text-muted-foreground">
                {i18n.language === 'ar' ? assistanceType.descriptionAr : assistanceType.descriptionEn}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge 
              variant={assistanceType.isActive ? "default" : "secondary"}
              className={assistanceType.isActive ? "bg-green-100 text-green-800" : ""}
            >
              {assistanceType.isActive ? 'Active' : 'Inactive'}
            </Badge>
            <Button variant="outline" onClick={() => router.push(`/admin/assistance-types/${typeId}/edit`)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button 
              variant={assistanceType.isActive ? "destructive" : "default"}
              onClick={() => {/* Toggle active status */}}
            >
              {assistanceType.isActive ? (
                <>
                  <PowerOff className="h-4 w-4 mr-2" />
                  Deactivate
                </>
              ) : (
                <>
                  <Power className="h-4 w-4 mr-2" />
                  Activate
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Overview Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Maximum Amount</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(assistanceType.maxAmount)}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Required Documents</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assistanceType.requiredDocuments.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Eligibility Criteria</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{assistanceType.eligibilityCriteria.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Category</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">{assistanceType.category}</div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="documents">Required Documents</TabsTrigger>
            <TabsTrigger value="eligibility">Eligibility Criteria</TabsTrigger>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium mb-2">Arabic Name</h4>
                    <p className="text-muted-foreground">{assistanceType.nameAr}</p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">English Name</h4>
                    <p className="text-muted-foreground">{assistanceType.nameEn}</p>
                  </div>
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium mb-2">Arabic Description</h4>
                    <p className="text-muted-foreground">{assistanceType.descriptionAr}</p>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">English Description</h4>
                    <p className="text-muted-foreground">{assistanceType.descriptionEn}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Required Documents</CardTitle>
                <CardDescription>
                  Documents that applicants must provide for this assistance type
                </CardDescription>
              </CardHeader>
              <CardContent>
                {assistanceType.requiredDocuments.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    No required documents configured
                  </p>
                ) : (
                  <div className="space-y-4">
                    {assistanceType.requiredDocuments.map((doc, index) => (
                      <Card key={doc.id} className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">
                                {i18n.language === 'ar' ? doc.nameAr : doc.nameEn}
                              </h4>
                              <Badge variant={doc.isRequired ? "default" : "secondary"}>
                                {doc.isRequired ? 'Required' : 'Optional'}
                              </Badge>
                            </div>
                            <div className="text-sm text-muted-foreground space-y-1">
                              <p>Accepted formats: {doc.acceptedFormats.join(', ')}</p>
                              <p>Maximum size: {doc.maxSizeKB} KB</p>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="eligibility" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Eligibility Criteria</CardTitle>
                <CardDescription>
                  Criteria that applicants must meet to be eligible for this assistance
                </CardDescription>
              </CardHeader>
              <CardContent>
                {assistanceType.eligibilityCriteria.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    No eligibility criteria configured
                  </p>
                ) : (
                  <div className="space-y-4">
                    {assistanceType.eligibilityCriteria.map((criteria, index) => (
                      <Card key={index} className="p-4">
                        <div className="space-y-2">
                          <h4 className="font-medium">Criteria {index + 1}</h4>
                          <div className="text-sm text-muted-foreground space-y-1">
                            <p>Field: {criteria.field}</p>
                            <p>Condition: {criteria.condition}</p>
                            <p>Value: {criteria.value}</p>
                            {criteria.nationality && <p>Nationality: {criteria.nationality}</p>}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="statistics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Usage Statistics</CardTitle>
                <CardDescription>
                  Statistics and metrics for this assistance type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">45</div>
                    <p className="text-sm text-muted-foreground">Total Applications</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">38</div>
                    <p className="text-sm text-muted-foreground">Approved</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-red-600">7</div>
                    <p className="text-sm text-muted-foreground">Rejected</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
