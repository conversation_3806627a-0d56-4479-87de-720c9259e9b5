'use client'

import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { AssistanceTypeForm } from '@/components/admin/assistance-type-form'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import type { AssistanceType, RequiredDocument } from '@/lib/types'

export default function NewAssistanceTypePage() {
  const router = useRouter()
  const { t } = useTranslation()
  const { toast } = useToast()

  const handleSubmit = async (data: {
    nameAr: string
    nameEn: string
    descriptionAr: string
    descriptionEn: string
    maxAmount: number
    category: string
    isActive: boolean
    requiredDocuments: RequiredDocument[]
  }) => {
    try {
      // In a real app, this would be an API call
      console.log('Creating assistance type:', data)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: 'Success',
        description: 'Assistance type created successfully',
      })
      
      router.push('/admin/assistance-types')
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create assistance type',
        variant: 'destructive',
      })
    }
  }

  const handleCancel = () => {
    router.push('/admin/assistance-types')
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Assistance Types
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Create New Assistance Type
            </h1>
            <p className="text-muted-foreground">
              Configure a new assistance type with eligibility criteria and requirements
            </p>
          </div>
        </div>

        {/* Form */}
        <AssistanceTypeForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
        />
      </div>
    </AdminLayout>
  )
}
