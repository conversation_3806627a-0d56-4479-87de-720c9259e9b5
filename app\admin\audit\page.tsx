'use client'

import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Search, 
  Filter, 
  Download, 
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'

// Mock audit trail data
const mockAuditLogs = [
  {
    id: '1',
    timestamp: '2024-01-15T10:30:00Z',
    user: '<PERSON>',
    action: 'CREATE_ASSISTANCE_TYPE',
    entity: 'Financial Support',
    details: 'Created new assistance type: Emergency Financial Aid',
    ipAddress: '*************',
    severity: 'info'
  },
  {
    id: '2',
    timestamp: '2024-01-15T09:15:00Z',
    user: 'Fatima Al-Zahra',
    action: 'UPDATE_USER_ROLE',
    entity: 'User: <EMAIL>',
    details: 'Changed user role from Researcher to Banking Expert',
    ipAddress: '*************',
    severity: 'warning'
  },
  {
    id: '3',
    timestamp: '2024-01-15T08:45:00Z',
    user: 'System',
    action: 'WORKFLOW_EXECUTION',
    entity: 'Request: REQ-2024-001',
    details: 'Automatically routed request to Banking Expert based on amount threshold',
    ipAddress: 'system',
    severity: 'info'
  },
  {
    id: '4',
    timestamp: '2024-01-14T16:20:00Z',
    user: 'Omar Hassan',
    action: 'APPROVE_REQUEST',
    entity: 'Request: REQ-2024-002',
    details: 'Approved financial assistance request for SAR 15,000',
    ipAddress: '*************',
    severity: 'info'
  },
  {
    id: '5',
    timestamp: '2024-01-14T14:10:00Z',
    user: 'System',
    action: 'FAILED_LOGIN_ATTEMPT',
    entity: 'User: <EMAIL>',
    details: 'Multiple failed login attempts detected',
    ipAddress: '***********',
    severity: 'error'
  }
]

export default function AuditTrailPage() {
  const { t } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')
  const [auditLogs] = useState(mockAuditLogs)

  const filteredLogs = auditLogs.filter(log => {
    const searchLower = searchTerm.toLowerCase()
    return log.user.toLowerCase().includes(searchLower) ||
           log.action.toLowerCase().includes(searchLower) ||
           log.entity.toLowerCase().includes(searchLower) ||
           log.details.toLowerCase().includes(searchLower)
  })

  const getSeverityColor = (severity: string) => {
    const colors: Record<string, string> = {
      'info': 'bg-blue-100 text-blue-800',
      'warning': 'bg-yellow-100 text-yellow-800',
      'error': 'bg-red-100 text-red-800',
      'success': 'bg-green-100 text-green-800'
    }
    return colors[severity] || 'bg-gray-100 text-gray-800'
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return <AlertTriangle className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      case 'success':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  const handleExport = () => {
    // Mock export functionality
    console.log('Exporting audit logs...')
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('audit_trail')}
            </h1>
            <p className="text-muted-foreground">
              {t('view_system_activity_logs_compliance')}
            </p>
          </div>
          <Button onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            {t('export_logs')}
          </Button>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center">
              <div className="relative flex-1">
                <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={t('search_audit_logs_placeholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                {t('advanced_filters')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('total_events')}</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{auditLogs.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('security_events')}</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {auditLogs.filter(log => log.severity === 'error').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('warnings')}</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {auditLogs.filter(log => log.severity === 'warning').length}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('todays_events')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {auditLogs.filter(log =>
                  new Date(log.timestamp).toDateString() === new Date().toDateString()
                ).length}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Audit Logs Table */}
        <Card>
          <CardHeader>
            <CardTitle>{t('system_activity_logs')}</CardTitle>
            <CardDescription>
              {t('showing_audit_events', { count: filteredLogs.length, total: auditLogs.length })}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('timestamp')}</TableHead>
                    <TableHead>{t('user')}</TableHead>
                    <TableHead>{t('action')}</TableHead>
                    <TableHead>{t('entity')}</TableHead>
                    <TableHead>{t('details')}</TableHead>
                    <TableHead>{t('severity')}</TableHead>
                    <TableHead>{t('ip_address')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-muted-foreground">
                          {t('no_audit_logs_found')}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell className="font-mono text-sm">
                          {formatTimestamp(log.timestamp)}
                        </TableCell>
                        <TableCell className="font-medium">
                          {log.user}
                        </TableCell>
                        <TableCell>
                          <code className="text-xs bg-muted px-2 py-1 rounded">
                            {log.action}
                          </code>
                        </TableCell>
                        <TableCell className="max-w-[200px] truncate">
                          {log.entity}
                        </TableCell>
                        <TableCell className="max-w-[300px] truncate">
                          {log.details}
                        </TableCell>
                        <TableCell>
                          <Badge className={getSeverityColor(log.severity)}>
                            <span className="flex items-center gap-1">
                              {getSeverityIcon(log.severity)}
                              {log.severity}
                            </span>
                          </Badge>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {log.ipAddress}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>{t('quick_actions')}</CardTitle>
            <CardDescription>
              {t('common_audit_operations_reports')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                <div className="font-medium">{t('security_report')}</div>
                <div className="text-sm text-muted-foreground">
                  {t('generate_security_events_report')}
                </div>
              </Button>
              <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                <div className="font-medium">{t('user_activity_report')}</div>
                <div className="text-sm text-muted-foreground">
                  {t('export_user_activity_summary')}
                </div>
              </Button>
              <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
                <div className="font-medium">{t('compliance_report')}</div>
                <div className="text-sm text-muted-foreground">
                  {t('generate_compliance_audit_report')}
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
