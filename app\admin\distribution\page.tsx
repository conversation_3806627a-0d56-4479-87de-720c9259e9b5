'use client'

import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  Plus, 
  BarChart3, 
  Settings, 
  Users,
  Clock,
  Target,
  Activity
} from 'lucide-react'

export default function DistributionManagementPage() {
  const { t } = useTranslation()

  const mockStaffWorkload = [
    { name: '<PERSON>', role: 'Reception Staff', currentLoad: 12, maxCapacity: 15, efficiency: 92 },
    { name: '<PERSON><PERSON>', role: 'Researcher', currentLoad: 8, maxCapacity: 10, efficiency: 88 },
    { name: '<PERSON>', role: 'Banking Expert', currentLoad: 5, maxCapacity: 8, efficiency: 95 },
    { name: '<PERSON><PERSON>', role: 'Reception Staff', currentLoad: 14, maxCapacity: 15, efficiency: 85 },
  ]

  const distributionRules = [
    {
      id: 'round-robin',
      name: 'Round Robin Distribution',
      description: 'Distribute requests evenly among available staff',
      isActive: true,
      requestsProcessed: 234
    },
    {
      id: 'specialization-based',
      name: 'Specialization-Based',
      description: 'Route requests based on staff expertise',
      isActive: true,
      requestsProcessed: 156
    },
    {
      id: 'workload-balanced',
      name: 'Workload Balanced',
      description: 'Consider current workload when assigning',
      isActive: false,
      requestsProcessed: 89
    }
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Distribution Management
            </h1>
            <p className="text-muted-foreground">
              Configure automatic application distribution and workload management
            </p>
          </div>
          <Button asChild>
            <Plus className="mr-2 h-4 w-4" />
            Create Distribution Rule
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Staff</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockStaffWorkload.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Workload</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(mockStaffWorkload.reduce((sum, s) => sum + (s.currentLoad / s.maxCapacity * 100), 0) / mockStaffWorkload.length)}%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2.4h</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Efficiency Score</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {Math.round(mockStaffWorkload.reduce((sum, s) => sum + s.efficiency, 0) / mockStaffWorkload.length)}%
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="workload" className="space-y-4">
          <TabsList>
            <TabsTrigger value="workload">Workload Monitor</TabsTrigger>
            <TabsTrigger value="rules">Distribution Rules</TabsTrigger>
            <TabsTrigger value="assignment">Manual Assignment</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          {/* Workload Monitor Tab */}
          <TabsContent value="workload" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Staff Workload Overview</CardTitle>
                <CardDescription>
                  Real-time monitoring of staff workload and capacity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockStaffWorkload.map((staff, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{staff.name}</h4>
                          <p className="text-sm text-muted-foreground">{staff.role}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">
                            {staff.currentLoad}/{staff.maxCapacity} requests
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {staff.efficiency}% efficiency
                          </div>
                        </div>
                      </div>
                      <Progress 
                        value={(staff.currentLoad / staff.maxCapacity) * 100} 
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Distribution Rules Tab */}
          <TabsContent value="rules" className="space-y-4">
            <div className="grid gap-4">
              {distributionRules.map((rule) => (
                <Card key={rule.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-3">
                          <h3 className="text-lg font-semibold">{rule.name}</h3>
                          <Badge variant={rule.isActive ? "default" : "secondary"}>
                            {rule.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground">{rule.description}</p>
                        <div className="text-sm text-muted-foreground">
                          {rule.requestsProcessed} requests processed
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4 mr-2" />
                          Configure
                        </Button>
                        <Button variant="outline" size="sm">
                          <Activity className="h-4 w-4 mr-2" />
                          Test
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Manual Assignment Tab */}
          <TabsContent value="assignment" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Manual Assignment Override</CardTitle>
                <CardDescription>
                  Manually assign or reassign requests when needed
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="mx-auto h-12 w-12 mb-4" />
                  <p>Manual assignment interface</p>
                  <p className="text-sm">Override automatic distribution when needed</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Distribution Analytics</CardTitle>
                <CardDescription>
                  Analyze distribution patterns and performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <BarChart3 className="mx-auto h-12 w-12 mb-4" />
                  <p>Distribution analytics dashboard</p>
                  <p className="text-sm">Performance metrics and insights</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
