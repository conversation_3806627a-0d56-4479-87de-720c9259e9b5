'use client'

import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Eye, 
  MoreHorizontal,
  FileText,
  Settings
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import Link from 'next/link'

// Mock document types data
const mockDocumentTypes = [
  {
    id: 'salary-certificate',
    nameAr: 'شهادة راتب',
    nameEn: 'Salary Certificate',
    descriptionAr: 'شهادة تثبت الراتب الشهري للموظف',
    descriptionEn: 'Certificate proving employee monthly salary',
    acceptedFormats: ['pdf', 'jpg', 'png'],
    maxSizeKB: 2048,
    isActive: true,
    usageCount: 45
  },
  {
    id: 'bank-statement',
    nameAr: 'كشف حساب بنكي',
    nameEn: 'Bank Statement',
    descriptionAr: 'كشف حساب بنكي للأشهر الثلاثة الماضية',
    descriptionEn: 'Bank statement for the last three months',
    acceptedFormats: ['pdf'],
    maxSizeKB: 5120,
    isActive: true,
    usageCount: 38
  },
  {
    id: 'medical-report',
    nameAr: 'تقرير طبي',
    nameEn: 'Medical Report',
    descriptionAr: 'تقرير طبي معتمد من مستشفى أو عيادة',
    descriptionEn: 'Certified medical report from hospital or clinic',
    acceptedFormats: ['pdf', 'jpg', 'png'],
    maxSizeKB: 3072,
    isActive: true,
    usageCount: 22
  },
  {
    id: 'housing-contract',
    nameAr: 'عقد إيجار',
    nameEn: 'Housing Contract',
    descriptionAr: 'عقد إيجار السكن الحالي',
    descriptionEn: 'Current housing rental contract',
    acceptedFormats: ['pdf'],
    maxSizeKB: 2048,
    isActive: false,
    usageCount: 15
  }
]

export default function DocumentConfigurationPage() {
  const { t, i18n } = useTranslation()
  const [searchTerm, setSearchTerm] = useState('')
  const [documentTypes, setDocumentTypes] = useState(mockDocumentTypes)

  const filteredTypes = documentTypes.filter(type => {
    const searchLower = searchTerm.toLowerCase()
    const nameMatch = (i18n.language === 'ar' ? type.nameAr : type.nameEn)
      .toLowerCase().includes(searchLower)
    const descMatch = (i18n.language === 'ar' ? type.descriptionAr : type.descriptionEn)
      .toLowerCase().includes(searchLower)
    return nameMatch || descMatch
  })

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('document_configuration')}
            </h1>
            <p className="text-muted-foreground">
              {t('manage_document_types_requirements')}
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/documents/new">
              <Plus className="mr-2 h-4 w-4" />
              {t('add_document_type')}
            </Link>
          </Button>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="types" className="space-y-4">
          <TabsList>
            <TabsTrigger value="types">{t('document_types')}</TabsTrigger>
            <TabsTrigger value="requirements">{t('requirements_mapping')}</TabsTrigger>
            <TabsTrigger value="validation">{t('validation_rules')}</TabsTrigger>
          </TabsList>

          {/* Document Types Tab */}
          <TabsContent value="types" className="space-y-4">
            {/* Search and Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col gap-4 md:flex-row md:items-center">
                  <div className="relative flex-1">
                    <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder={t('search_document_types_placeholder')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pr-10"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    {t('filter')}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Statistics Cards */}
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('total_types')}</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{documentTypes.length}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('active_types')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {documentTypes.filter(t => t.isActive).length}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('most_used')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-lg font-bold">
                    {documentTypes.reduce((max, type) =>
                      type.usageCount > max.usageCount ? type : max
                    )[i18n.language === 'ar' ? 'nameAr' : 'nameEn']}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">{t('total_usage')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {documentTypes.reduce((sum, type) => sum + type.usageCount, 0)}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Document Types Table */}
            <Card>
              <CardHeader>
                <CardTitle>{t('document_types')}</CardTitle>
                <CardDescription>
                  {t('showing_document_types', { count: filteredTypes.length, total: documentTypes.length })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('name')}</TableHead>
                        <TableHead>{t('accepted_formats')}</TableHead>
                        <TableHead>{t('max_size')}</TableHead>
                        <TableHead>{t('usage_count')}</TableHead>
                        <TableHead>{t('status')}</TableHead>
                        <TableHead className="text-center">{t('actions')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredTypes.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="text-muted-foreground">
                              {t('no_document_types_found')}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredTypes.map((type) => (
                          <TableRow key={type.id}>
                            <TableCell>
                              <div>
                                <div className="font-medium">
                                  {i18n.language === 'ar' ? type.nameAr : type.nameEn}
                                </div>
                                <div className="text-sm text-muted-foreground line-clamp-1">
                                  {i18n.language === 'ar' ? type.descriptionAr : type.descriptionEn}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-1">
                                {type.acceptedFormats.map(format => (
                                  <Badge key={format} variant="outline" className="text-xs">
                                    {format.toUpperCase()}
                                  </Badge>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell className="font-mono">
                              {(type.maxSizeKB / 1024).toFixed(1)} MB
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">
                                {type.usageCount} {t('uses')}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant={type.isActive ? "default" : "secondary"}
                                className={type.isActive ? "bg-green-100 text-green-800" : ""}
                              >
                                {type.isActive ? t('status_active') : t('status_inactive')}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-center">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>{t('actions')}</DropdownMenuLabel>
                                  <DropdownMenuItem asChild>
                                    <Link href={`/admin/documents/${type.id}`}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      {t('view_details')}
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem asChild>
                                    <Link href={`/admin/documents/${type.id}/edit`}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      {t('edit')}
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem>
                                    <Settings className="mr-2 h-4 w-4" />
                                    {t('configure_requirements')}
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Requirements Mapping Tab */}
          <TabsContent value="requirements" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Document Requirements Mapping</CardTitle>
                <CardDescription>
                  Configure which documents are required for each assistance type
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="mx-auto h-12 w-12 mb-4" />
                  <p>Requirements mapping interface</p>
                  <p className="text-sm">Configure document requirements per assistance type</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Validation Rules Tab */}
          <TabsContent value="validation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Validation Rules</CardTitle>
                <CardDescription>
                  Configure file format and size validation rules
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Settings className="mx-auto h-12 w-12 mb-4" />
                  <p>Validation rules interface</p>
                  <p className="text-sm">Configure file validation parameters</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
