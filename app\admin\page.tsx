'use client'

import { useSession } from 'next-auth/react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Users, 
  CreditCard, 
  Settings, 
  Workflow, 
  FileText, 
  Shield,
  BarChart3,
  Database
} from 'lucide-react'
import Link from 'next/link'

export default function AdminDashboardPage() {
  const { data: session } = useSession() || {}
  const { t } = useTranslation()

  if (!session?.user) {
    return null
  }

  // Only system admins can access this page
  if (session.user.role !== 'system_admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">{t('access_denied')}</h2>
          <p className="text-muted-foreground">{t('admin_access_required')}</p>
        </div>
      </div>
    )
  }

  const adminSections = [
    {
      title: t('user_management'),
      description: t('manage_user_accounts_roles'),
      icon: Users,
      href: '/admin/users',
      color: 'bg-blue-500'
    },
    {
      title: t('assistance_types'),
      description: t('configure_aid_types_eligibility'),
      icon: CreditCard,
      href: '/admin/assistance-types',
      color: 'bg-green-500'
    },
    {
      title: t('workflow_management'),
      description: t('configure_approval_workflows'),
      icon: Workflow,
      href: '/admin/workflow',
      color: 'bg-purple-500'
    },
    {
      title: t('document_configuration'),
      description: t('manage_document_requirements'),
      icon: FileText,
      href: '/admin/documents',
      color: 'bg-orange-500'
    },
    {
      title: t('distribution_rules'),
      description: t('configure_application_distribution'),
      icon: BarChart3,
      href: '/admin/distribution',
      color: 'bg-indigo-500'
    },
    {
      title: t('system_settings'),
      description: t('general_system_configuration'),
      icon: Settings,
      href: '/admin/settings',
      color: 'bg-gray-500'
    },
    {
      title: t('audit_trail'),
      description: t('view_system_activity_logs'),
      icon: Shield,
      href: '/admin/audit',
      color: 'bg-red-500'
    },
    {
      title: t('data_management'),
      description: t('import_export_backup'),
      icon: Database,
      href: '/admin/data',
      color: 'bg-teal-500'
    }
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {t('system_administration')}
          </h1>
          <p className="text-muted-foreground">
            {t('system_administration_overview')}
          </p>
        </div>

        {/* Admin Sections Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {adminSections.map((section) => (
            <Card key={section.href} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${section.color} text-white`}>
                    <section.icon className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <CardDescription className="text-sm">
                  {section.description}
                </CardDescription>
                <Button asChild className="w-full">
                  <Link href={section.href}>
                    Manage
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">150</div>
              <p className="text-xs text-muted-foreground">
                +12 from last month
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Aid Types</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                3 recently updated
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
              <Workflow className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">
                All operational
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Good</div>
              <p className="text-xs text-muted-foreground">
                All systems operational
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
