'use client'

import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Settings, 
  Building, 
  Calculator,
  Bell,
  Database,
  Shield
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function SystemSettingsPage() {
  const { t } = useTranslation()
  const { toast } = useToast()

  const [settings, setSettings] = useState({
    // General Settings
    organizationName: 'Ministry of Social Development',
    organizationNameAr: 'وزارة التنمية الاجتماعية',
    contactEmail: '<EMAIL>',
    contactPhone: '+966-11-123-4567',
    address: 'Riyadh, Saudi Arabia',
    
    // Zakat Calculation Settings
    nisabGold: 85, // grams
    nisabSilver: 595, // grams
    zakatRate: 2.5, // percentage
    hijriCalendar: true,
    
    // Notification Settings
    emailNotifications: true,
    smsNotifications: true,
    systemAlerts: true,
    
    // System Limits
    maxRequestAmount: 100000,
    sessionTimeout: 30, // minutes
    maxFileSize: 5, // MB
    
    // Backup Settings
    autoBackup: true,
    backupFrequency: 'daily',
    retentionPeriod: 90 // days
  })

  const handleSave = async () => {
    try {
      // In a real app, this would be an API call
      console.log('Saving settings:', settings)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: t('success'),
        description: t('system_settings_saved_successfully'),
      })
    } catch (error) {
      toast({
        title: t('error'),
        description: t('failed_to_save_settings'),
        variant: 'destructive',
      })
    }
  }

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('system_settings')}
            </h1>
            <p className="text-muted-foreground">
              {t('configure_general_system_parameters')}
            </p>
          </div>
          <Button onClick={handleSave}>
            {t('save_changes')}
          </Button>
        </div>

        {/* Settings Tabs */}
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general">{t('general')}</TabsTrigger>
            <TabsTrigger value="calculation">{t('calculation')}</TabsTrigger>
            <TabsTrigger value="notifications">{t('notifications')}</TabsTrigger>
            <TabsTrigger value="limits">{t('limits')}</TabsTrigger>
            <TabsTrigger value="backup">{t('backup')}</TabsTrigger>
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {t('organization_information')}
                </CardTitle>
                <CardDescription>
                  {t('basic_organization_contact_info')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="orgName">{t('organization_name_english')}</Label>
                    <Input
                      id="orgName"
                      value={settings.organizationName}
                      onChange={(e) => updateSetting('organizationName', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="orgNameAr">{t('organization_name_arabic')}</Label>
                    <Input
                      id="orgNameAr"
                      value={settings.organizationNameAr}
                      onChange={(e) => updateSetting('organizationNameAr', e.target.value)}
                      dir="rtl"
                    />
                  </div>
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="email">{t('contact_email')}</Label>
                    <Input
                      id="email"
                      type="email"
                      value={settings.contactEmail}
                      onChange={(e) => updateSetting('contactEmail', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">{t('contact_phone')}</Label>
                    <Input
                      id="phone"
                      value={settings.contactPhone}
                      onChange={(e) => updateSetting('contactPhone', e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="address">{t('address')}</Label>
                  <Textarea
                    id="address"
                    value={settings.address}
                    onChange={(e) => updateSetting('address', e.target.value)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Calculation Settings */}
          <TabsContent value="calculation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Zakat Calculation Parameters
                </CardTitle>
                <CardDescription>
                  Configure Nisab values and calculation methods
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="nisabGold">Nisab Gold (grams)</Label>
                    <Input
                      id="nisabGold"
                      type="number"
                      value={settings.nisabGold}
                      onChange={(e) => updateSetting('nisabGold', Number(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nisabSilver">Nisab Silver (grams)</Label>
                    <Input
                      id="nisabSilver"
                      type="number"
                      value={settings.nisabSilver}
                      onChange={(e) => updateSetting('nisabSilver', Number(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="zakatRate">Zakat Rate (%)</Label>
                    <Input
                      id="zakatRate"
                      type="number"
                      step="0.1"
                      value={settings.zakatRate}
                      onChange={(e) => updateSetting('zakatRate', Number(e.target.value))}
                    />
                  </div>
                </div>
                
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <Label className="text-base">Use Hijri Calendar</Label>
                    <div className="text-sm text-muted-foreground">
                      Use Islamic calendar for Zakat calculations
                    </div>
                  </div>
                  <Switch
                    checked={settings.hijriCalendar}
                    onCheckedChange={(checked) => updateSetting('hijriCalendar', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notification Settings */}
          <TabsContent value="notifications" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Preferences
                </CardTitle>
                <CardDescription>
                  Configure system notifications and alerts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <Label className="text-base">Email Notifications</Label>
                      <div className="text-sm text-muted-foreground">
                        Send notifications via email
                      </div>
                    </div>
                    <Switch
                      checked={settings.emailNotifications}
                      onCheckedChange={(checked) => updateSetting('emailNotifications', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <Label className="text-base">SMS Notifications</Label>
                      <div className="text-sm text-muted-foreground">
                        Send notifications via SMS
                      </div>
                    </div>
                    <Switch
                      checked={settings.smsNotifications}
                      onCheckedChange={(checked) => updateSetting('smsNotifications', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <Label className="text-base">System Alerts</Label>
                      <div className="text-sm text-muted-foreground">
                        Show in-app system alerts
                      </div>
                    </div>
                    <Switch
                      checked={settings.systemAlerts}
                      onCheckedChange={(checked) => updateSetting('systemAlerts', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Limits */}
          <TabsContent value="limits" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  System Limits and Thresholds
                </CardTitle>
                <CardDescription>
                  Configure operational limits and security parameters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="maxAmount">Max Request Amount (SAR)</Label>
                    <Input
                      id="maxAmount"
                      type="number"
                      value={settings.maxRequestAmount}
                      onChange={(e) => updateSetting('maxRequestAmount', Number(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                    <Input
                      id="sessionTimeout"
                      type="number"
                      value={settings.sessionTimeout}
                      onChange={(e) => updateSetting('sessionTimeout', Number(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxFileSize">Max File Size (MB)</Label>
                    <Input
                      id="maxFileSize"
                      type="number"
                      value={settings.maxFileSize}
                      onChange={(e) => updateSetting('maxFileSize', Number(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Backup Settings */}
          <TabsContent value="backup" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Backup and Maintenance
                </CardTitle>
                <CardDescription>
                  Configure backup settings and data retention policies
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <Label className="text-base">Automatic Backup</Label>
                    <div className="text-sm text-muted-foreground">
                      Enable automatic system backups
                    </div>
                  </div>
                  <Switch
                    checked={settings.autoBackup}
                    onCheckedChange={(checked) => updateSetting('autoBackup', checked)}
                  />
                </div>
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="backupFreq">Backup Frequency</Label>
                    <select
                      id="backupFreq"
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                      value={settings.backupFrequency}
                      onChange={(e) => updateSetting('backupFrequency', e.target.value)}
                    >
                      <option value="hourly">Hourly</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="retention">Retention Period (days)</Label>
                    <Input
                      id="retention"
                      type="number"
                      value={settings.retentionPeriod}
                      onChange={(e) => updateSetting('retentionPeriod', Number(e.target.value))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
