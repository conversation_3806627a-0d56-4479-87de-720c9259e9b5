'use client'

import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { AdminLayout } from '@/components/admin/admin-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Plus, 
  Workflow, 
  Settings, 
  Users,
  TrendingUp,
  Play
} from 'lucide-react'
import Link from 'next/link'

export default function WorkflowManagementPage() {
  const { t } = useTranslation()

  const mockWorkflows = [
    {
      id: 'financial-workflow',
      name: t('financial_support_workflow'),
      description: t('standard_workflow_financial_assistance'),
      steps: 5,
      isActive: true,
      requestsProcessed: 145
    },
    {
      id: 'medical-workflow',
      name: t('medical_assistance_workflow'),
      description: t('specialized_workflow_medical_aid'),
      steps: 4,
      isActive: true,
      requestsProcessed: 89
    },
    {
      id: 'emergency-workflow',
      name: t('emergency_relief_workflow'),
      description: t('fast_track_workflow_emergency'),
      steps: 3,
      isActive: true,
      requestsProcessed: 23
    }
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {t('workflow_management')}
            </h1>
            <p className="text-muted-foreground">
              {t('configure_approval_workflows_business_rules')}
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/workflow/new">
              <Plus className="mr-2 h-4 w-4" />
              {t('create_workflow')}
            </Link>
          </Button>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('active_workflows')}</CardTitle>
              <Workflow className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockWorkflows.filter(w => w.isActive).length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('total_requests')}</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {mockWorkflows.reduce((sum, w) => sum + w.requestsProcessed, 0)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('avg_steps')}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(mockWorkflows.reduce((sum, w) => sum + w.steps, 0) / mockWorkflows.length).toFixed(1)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('active_roles')}</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">6</div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="workflows" className="space-y-4">
          <TabsList>
            <TabsTrigger value="workflows">{t('workflows')}</TabsTrigger>
            <TabsTrigger value="roles">{t('role_configuration')}</TabsTrigger>
            <TabsTrigger value="rules">{t('business_rules')}</TabsTrigger>
            <TabsTrigger value="testing">{t('testing_simulation')}</TabsTrigger>
          </TabsList>

          {/* Workflows Tab */}
          <TabsContent value="workflows" className="space-y-4">
            <div className="grid gap-4">
              {mockWorkflows.map((workflow) => (
                <Card key={workflow.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-3">
                          <h3 className="text-lg font-semibold">{workflow.name}</h3>
                          <Badge variant={workflow.isActive ? "default" : "secondary"}>
                            {workflow.isActive ? t('status_active') : t('status_inactive')}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground">{workflow.description}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>{workflow.steps} {t('steps')}</span>
                          <span>{workflow.requestsProcessed} {t('requests_processed')}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4 mr-2" />
                          {t('configure')}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Play className="h-4 w-4 mr-2" />
                          {t('test')}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Role Configuration Tab */}
          <TabsContent value="roles" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Role Competencies</CardTitle>
                <CardDescription>
                  Configure role competencies, approval limits, and specialization areas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Users className="mx-auto h-12 w-12 mb-4" />
                  <p>Role configuration interface</p>
                  <p className="text-sm">Define role competencies and approval limits</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Business Rules Tab */}
          <TabsContent value="rules" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Business Rules</CardTitle>
                <CardDescription>
                  Configure value-based routing and conditional workflow logic
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Settings className="mx-auto h-12 w-12 mb-4" />
                  <p>Business rules interface</p>
                  <p className="text-sm">Set up conditional logic and routing rules</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Testing Tab */}
          <TabsContent value="testing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Workflow Testing</CardTitle>
                <CardDescription>
                  Test workflow configurations with sample requests
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <Play className="mx-auto h-12 w-12 mb-4" />
                  <p>Workflow testing interface</p>
                  <p className="text-sm">Simulate workflows with test data</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
