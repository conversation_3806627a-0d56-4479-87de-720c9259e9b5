@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Noto+Naskh+Arabic:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oxanium:wght@200;300;400;500;600;700;800&family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&family=Fira+Code:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: oklch(0.9885 0.0057 84.5659);
    --foreground: oklch(0.3660 0.0251 49.6085);
    --card: oklch(0.9686 0.0091 78.2818);
    --card-foreground: oklch(0.3660 0.0251 49.6085);
    --popover: oklch(0.9686 0.0091 78.2818);
    --popover-foreground: oklch(0.3660 0.0251 49.6085);
    --primary: oklch(0.5553 0.1455 48.9975);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.8276 0.0752 74.4400);
    --secondary-foreground: oklch(0.4444 0.0096 73.6390);
    --muted: oklch(0.9363 0.0218 83.2637);
    --muted-foreground: oklch(0.5534 0.0116 58.0708);
    --accent: oklch(0.9000 0.0500 74.9889);
    --accent-foreground: oklch(0.4444 0.0096 73.6390);
    --destructive: oklch(0.4437 0.1613 26.8994);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.8866 0.0404 89.6994);
    --input: oklch(0.8866 0.0404 89.6994);
    --ring: oklch(0.5553 0.1455 48.9975);
    --chart-1: oklch(0.5553 0.1455 48.9975);
    --chart-2: oklch(0.5534 0.0116 58.0708);
    --chart-3: oklch(0.5538 0.1207 66.4416);
    --chart-4: oklch(0.5534 0.0116 58.0708);
    --chart-5: oklch(0.6806 0.1423 75.8340);
    --sidebar: oklch(0.9363 0.0218 83.2637);
    --sidebar-foreground: oklch(0.3660 0.0251 49.6085);
    --sidebar-primary: oklch(0.5553 0.1455 48.9975);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.5538 0.1207 66.4416);
    --sidebar-accent-foreground: oklch(1.0000 0 0);
    --sidebar-border: oklch(0.8866 0.0404 89.6994);
    --sidebar-ring: oklch(0.5553 0.1455 48.9975);
    --font-sans: Oxanium, sans-serif;
    --font-serif: Merriweather, serif;
    --font-mono: Fira Code, monospace;
    --radius: 0.3rem;
    --shadow-2xs: 0px 2px 3px 0px hsl(28 18% 25% / 0.09);
    --shadow-xs: 0px 2px 3px 0px hsl(28 18% 25% / 0.09);
    --shadow-sm: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 1px 2px -1px hsl(28 18% 25% / 0.18);
    --shadow: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 1px 2px -1px hsl(28 18% 25% / 0.18);
    --shadow-md: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 2px 4px -1px hsl(28 18% 25% / 0.18);
    --shadow-lg: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 4px 6px -1px hsl(28 18% 25% / 0.18);
    --shadow-xl: 0px 2px 3px 0px hsl(28 18% 25% / 0.18), 0px 8px 10px -1px hsl(28 18% 25% / 0.18);
    --shadow-2xl: 0px 2px 3px 0px hsl(28 18% 25% / 0.45);
    --tracking-normal: 0em;
    --spacing: 0.25rem;

    /* Islamic-inspired custom colors for compatibility */
    --success: oklch(0.5538 0.1207 66.4416);
    --success-foreground: oklch(1.0000 0 0);
    --warning: oklch(0.6806 0.1423 75.8340);
    --warning-foreground: oklch(1.0000 0 0);
    --shadow-color: hsl(28 18% 25%);
    --shadow-opacity: 0.18;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
  }

  .dark {
    --background: oklch(0.2161 0.0061 56.0434);
    --foreground: oklch(0.9699 0.0013 106.4238);
    --card: oklch(0.2685 0.0063 34.2976);
    --card-foreground: oklch(0.9699 0.0013 106.4238);
    --popover: oklch(0.2685 0.0063 34.2976);
    --popover-foreground: oklch(0.9699 0.0013 106.4238);
    --primary: oklch(0.7049 0.1867 47.6044);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.4444 0.0096 73.6390);
    --secondary-foreground: oklch(0.9232 0.0026 48.7171);
    --muted: oklch(0.2685 0.0063 34.2976);
    --muted-foreground: oklch(0.7161 0.0091 56.2590);
    --accent: oklch(0.3598 0.0497 229.3202);
    --accent-foreground: oklch(0.9232 0.0026 48.7171);
    --destructive: oklch(0.5771 0.2152 27.3250);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3741 0.0087 67.5582);
    --input: oklch(0.3741 0.0087 67.5582);
    --ring: oklch(0.7049 0.1867 47.6044);
    --chart-1: oklch(0.7049 0.1867 47.6044);
    --chart-2: oklch(0.6847 0.1479 237.3225);
    --chart-3: oklch(0.7952 0.1617 86.0468);
    --chart-4: oklch(0.7161 0.0091 56.2590);
    --chart-5: oklch(0.5534 0.0116 58.0708);
    --sidebar: oklch(0.2685 0.0063 34.2976);
    --sidebar-foreground: oklch(0.9699 0.0013 106.4238);
    --sidebar-primary: oklch(0.7049 0.1867 47.6044);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.6847 0.1479 237.3225);
    --sidebar-accent-foreground: oklch(0.2839 0.0734 254.5378);
    --sidebar-border: oklch(0.3741 0.0087 67.5582);
    --sidebar-ring: oklch(0.7049 0.1867 47.6044);
    --font-sans: Oxanium, sans-serif;
    --font-serif: Merriweather, serif;
    --font-mono: Fira Code, monospace;
    --radius: 0.3rem;
    --shadow-2xs: 0px 2px 3px 0px hsl(0 0% 5% / 0.09);
    --shadow-xs: 0px 2px 3px 0px hsl(0 0% 5% / 0.09);
    --shadow-sm: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 1px 2px -1px hsl(0 0% 5% / 0.18);
    --shadow: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 1px 2px -1px hsl(0 0% 5% / 0.18);
    --shadow-md: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 2px 4px -1px hsl(0 0% 5% / 0.18);
    --shadow-lg: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 4px 6px -1px hsl(0 0% 5% / 0.18);
    --shadow-xl: 0px 2px 3px 0px hsl(0 0% 5% / 0.18), 0px 8px 10px -1px hsl(0 0% 5% / 0.18);
    --shadow-2xl: 0px 2px 3px 0px hsl(0 0% 5% / 0.45);

    /* Islamic-inspired custom colors for compatibility */
    --success: oklch(0.7952 0.1617 86.0468);
    --success-foreground: oklch(1.0000 0 0);
    --warning: oklch(0.6847 0.1479 237.3225);
    --warning-foreground: oklch(1.0000 0 0);
    --shadow-color: hsl(0 0% 5%);
    --shadow-opacity: 0.18;
    --shadow-blur: 3px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0em;
    --spacing: 0.25rem;
  }
  .theme {
    --font-sans: Oxanium, sans-serif;
    --font-mono: Fira Code, monospace;
    --font-serif: Merriweather, serif;
    --radius: 0.3rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Arabic text styling */
  [dir="rtl"] {
    font-family: 'Amiri', 'Noto Naskh Arabic', serif;
  }

  /* Enhanced typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl lg:text-4xl;
    line-height: 1.2;
  }

  h2 {
    @apply text-2xl lg:text-3xl;
    line-height: 1.3;
  }

  h3 {
    @apply text-xl lg:text-2xl;
    line-height: 1.4;
  }
}

@layer components {
  /* Enhanced card styling */
  .card-enhanced {
    @apply bg-card border border-border rounded-lg shadow-sm;
    transition: all 0.2s ease-in-out;
  }

  .card-enhanced:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary) 100%);
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success) 100%);
  }

  .bg-gradient-subtle {
    background: linear-gradient(135deg, var(--color-background) 0%, var(--color-muted) 100%);
  }

  /* Islamic geometric pattern (subtle) */
  .pattern-islamic {
    background-image:
      radial-gradient(circle at 25% 25%, color-mix(in oklch, var(--color-primary), transparent 95%) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, color-mix(in oklch, var(--color-success), transparent 95%) 0%, transparent 50%);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
  }

  /* Enhanced button styles */
  .btn-enhanced {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-all;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    @apply disabled:pointer-events-none disabled:opacity-50;
    box-shadow: var(--shadow-sm);
  }

  .btn-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  /* Enhanced input styles */
  .input-enhanced {
    @apply flex h-10 w-full rounded-lg border border-border bg-background px-3 py-2 text-sm;
    @apply ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium;
    @apply placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2;
    @apply focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
    transition: all 0.2s ease-in-out;
    box-shadow: var(--shadow-soft);
  }

  .input-enhanced:focus {
    border-color: hsl(var(--primary));
    box-shadow: var(--shadow-medium);
  }

  /* Islamic decorative elements */
  .islamic-border {
    position: relative;
  }

  .islamic-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary)), transparent);
  }

  /* Animated gradient text */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--success)));
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Floating animation */
  .float-animation {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Pulse animation for notifications */
  .pulse-success {
    animation: pulse-success 2s infinite;
  }

  @keyframes pulse-success {
    0%, 100% { box-shadow: 0 0 0 0 hsl(var(--success) / 0.7); }
    70% { box-shadow: 0 0 0 10px hsl(var(--success) / 0); }
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.3);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.5);
  }

  /* Selection styling */
  ::selection {
    background: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary));
  }

  /* Focus ring improvements */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Shimmer loading effect */
  .shimmer {
    background: linear-gradient(90deg,
      hsl(var(--muted)) 0%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 100%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
}