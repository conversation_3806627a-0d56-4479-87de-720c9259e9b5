'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

const pathLabels: Record<string, string> = {
  '/admin': 'Admin Dashboard',
  '/admin/users': 'User Management',
  '/admin/assistance-types': 'Assistance Types',
  '/admin/workflow': 'Workflow Management',
  '/admin/documents': 'Document Configuration',
  '/admin/distribution': 'Distribution Rules',
  '/admin/settings': 'System Settings',
  '/admin/audit': 'Audit Trail',
  '/admin/data': 'Data Management'
}

export function AdminBreadcrumb() {
  const pathname = usePathname()
  
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = []
    
    // Always start with Admin Dashboard
    breadcrumbs.push({
      label: 'Admin Dashboard',
      href: '/admin',
      isActive: pathname === '/admin'
    })
    
    // Build path progressively
    let currentPath = ''
    for (let i = 0; i < segments.length; i++) {
      currentPath += `/${segments[i]}`
      
      // Skip the first 'admin' segment since we already added it
      if (segments[i] === 'admin') continue
      
      const isLast = i === segments.length - 1
      const label = pathLabels[currentPath] || segments[i].replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      
      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentPath,
        isActive: isLast
      })
    }
    
    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
      <Home className="h-4 w-4" />
      
      {breadcrumbs.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && <ChevronRight className="h-4 w-4 mx-1" />}
          
          {item.href ? (
            <Link
              href={item.href}
              className="hover:text-foreground transition-colors"
            >
              {item.label}
            </Link>
          ) : (
            <span className={cn(
              "font-medium",
              item.isActive ? "text-foreground" : "text-muted-foreground"
            )}>
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  )
}
