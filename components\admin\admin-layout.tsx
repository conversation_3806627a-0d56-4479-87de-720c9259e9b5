'use client'

import { AdminSidebar } from './admin-sidebar'
import { AdminBreadcrumb } from './admin-breadcrumb'
import { AdminRouteGuard } from './admin-route-guard'
import { LanguageSwitcher } from '@/components/ui/language-switcher'
import { UserMenu } from '@/components/layout/user-menu'
import { ThemeToggle } from '@/components/ui/theme-toggle'

interface AdminLayoutProps {
  children: React.ReactNode
}

export function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <AdminRouteGuard>
    <div className="min-h-screen bg-background">
      <div className="flex">
        {/* Admin Sidebar */}
        <AdminSidebar />
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Top Header */}
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-16 items-center justify-between px-6">
              {/* Breadcrumb */}
              <AdminBreadcrumb />
              
              {/* Right side controls */}
              <div className="flex items-center gap-4">
                <LanguageSwitcher />
                <ThemeToggle />
                <UserMenu />
              </div>
            </div>
          </header>

          {/* Page Content */}
          <main className="flex-1 p-6">
            {children}
          </main>
        </div>
      </div>
    </div>
    </AdminRouteGuard>
  )
}
