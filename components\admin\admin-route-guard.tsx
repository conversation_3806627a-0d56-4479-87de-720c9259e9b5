'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

interface AdminRouteGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function AdminRouteGuard({ children, fallback }: AdminRouteGuardProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { t } = useTranslation()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (!session?.user) {
      router.push('/auth/login')
      return
    }

    if (session.user.role !== 'system_admin') {
      router.push('/dashboard')
      return
    }
  }, [session, status, router])

  // Show loading state
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t('loading')}</p>
        </div>
      </div>
    )
  }

  // Show unauthorized state
  if (!session?.user || session.user.role !== 'system_admin') {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="h-16 w-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
            <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold mb-2">{t('access_denied')}</h2>
            <p className="text-muted-foreground mb-4">{t('admin_access_required')}</p>
            <button 
              onClick={() => router.push('/dashboard')}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              {t('back_to_dashboard')}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
