'use client'

import { usePathname } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { 
  LayoutDashboard,
  Users, 
  CreditCard, 
  Settings, 
  Workflow, 
  FileText, 
  Shield,
  BarChart3,
  Database,
  ArrowLeft,
  Home
} from 'lucide-react'

interface AdminSidebarItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  description?: string
}

const getAdminSidebarItems = (t: any): AdminSidebarItem[] => [
  {
    title: t('admin_dashboard_nav'),
    href: '/admin',
    icon: LayoutDashboard,
    description: t('system_administration_overview')
  },
  {
    title: t('user_management'),
    href: '/admin/users',
    icon: Users,
    description: t('manage_user_accounts_roles')
  },
  {
    title: t('assistance_types'),
    href: '/admin/assistance-types',
    icon: Credit<PERSON>ard,
    description: t('configure_aid_types_eligibility')
  },
  {
    title: t('workflow_management'),
    href: '/admin/workflow',
    icon: Workflow,
    description: t('configure_approval_workflows')
  },
  {
    title: t('document_configuration'),
    href: '/admin/documents',
    icon: FileText,
    description: t('manage_document_requirements')
  },
  {
    title: t('distribution_rules'),
    href: '/admin/distribution',
    icon: BarChart3,
    description: t('configure_application_distribution')
  },
  {
    title: t('system_settings'),
    href: '/admin/settings',
    icon: Settings,
    description: t('general_system_configuration')
  },
  {
    title: t('audit_trail'),
    href: '/admin/audit',
    icon: Shield,
    description: t('view_system_activity_logs')
  },
  {
    title: t('data_management'),
    href: '/admin/data',
    icon: Database,
    description: t('import_export_backup')
  }
]

export function AdminSidebar() {
  const pathname = usePathname()
  const { t } = useTranslation()
  const adminSidebarItems = getAdminSidebarItems(t)

  return (
    <div className="w-64 border-r bg-muted/10 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center gap-2 mb-4">
          <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">ز</span>
          </div>
          <div>
            <h2 className="font-semibold text-sm">{t('system_name')}</h2>
            <p className="text-xs text-muted-foreground">{t('system_administration')}</p>
          </div>
        </div>

        {/* Back to Main System */}
        <Button variant="outline" size="sm" asChild className="w-full">
          <Link href="/dashboard">
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('back_to_dashboard')}
          </Link>
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {adminSidebarItems.map((item) => {
          const isActive = pathname === item.href
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                "hover:bg-accent hover:text-accent-foreground",
                isActive 
                  ? "bg-accent text-accent-foreground font-medium" 
                  : "text-muted-foreground"
              )}
            >
              <item.icon className="h-4 w-4" />
              <div className="flex-1">
                <div className="font-medium">{item.title}</div>
                {item.description && (
                  <div className="text-xs text-muted-foreground mt-0.5">
                    {item.description}
                  </div>
                )}
              </div>
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <div className="text-xs text-muted-foreground text-center">
          {t('system_administration')}
          <br />
          Version 1.0.0
        </div>
      </div>
    </div>
  )
}
