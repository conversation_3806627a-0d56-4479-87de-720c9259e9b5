'use client'

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { useTranslation } from 'react-i18next'
import { formatDistanceToNow } from 'date-fns'
import { ar, enUS } from 'date-fns/locale'
import {
  UserPlus,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface ActivityItem {
  id: string
  type: 'registration' | 'approval' | 'distribution' | 'review'
  title: string
  description: string
  timestamp: Date
  status: 'completed' | 'pending' | 'warning'
  user?: string
}

// Mock data - replace with real data from your mock-data.ts
const getMockActivities = (t: any): ActivityItem[] => [
  {
    id: '1',
    type: 'registration',
    title: t('New beneficiary registered'),
    description: t('<PERSON> has been registered'),
    timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
    status: 'completed',
    user: t('Reception Staff')
  },
  {
    id: '2',
    type: 'approval',
    title: t('Application approved'),
    description: t('Fatima Al-Zahra application approved for Zakat distribution'),
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
    status: 'completed',
    user: t('Case Manager')
  },
  {
    id: '3',
    type: 'distribution',
    title: t('Zakat distributed'),
    description: t('5,000 SAR distributed to 10 beneficiaries'),
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
    status: 'completed',
    user: t('Finance Manager')
  },
  {
    id: '4',
    type: 'review',
    title: t('Pending review'),
    description: t('3 applications require case manager review'),
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
    status: 'pending',
    user: t('System')
  }
]

export function RecentActivity() {
  const { t, i18n } = useTranslation()
  const isArabic = i18n.language === 'ar'
  const mockActivities = getMockActivities(t)

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'registration': return UserPlus
      case 'approval': return CheckCircle
      case 'distribution': return CreditCard
      case 'review': return AlertCircle
      default: return Clock
    }
  }

  const getStatusColor = (status: ActivityItem['status']) => {
    switch (status) {
      case 'completed': return 'default'
      case 'pending': return 'secondary'
      case 'warning': return 'destructive'
      default: return 'secondary'
    }
  }

  return (
    <Card className="card-enhanced border-0 bg-card/80 backdrop-blur-sm">
      <CardHeader className="flex flex-row items-center justify-between pb-4">
        <CardTitle className="text-xl font-semibold flex items-center space-x-2 rtl:space-x-reverse">
          <div className="h-8 w-8 rounded-lg bg-gradient-success flex items-center justify-center">
            <Clock className="h-4 w-4 text-success-foreground" />
          </div>
          <span>{t('recent_activity')}</span>
        </CardTitle>
        <Button variant="ghost" size="sm" className="text-primary hover:text-primary-light hover:bg-primary/10">
          {t('view_all')}
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockActivities.map((activity) => {
            const Icon = getActivityIcon(activity.type)
            return (
              <div key={activity.id} className="group flex items-start space-x-3 rtl:space-x-reverse p-3 rounded-lg hover:bg-accent/50 transition-colors">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <Icon className="h-5 w-5 text-primary" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm font-semibold text-foreground">{activity.title}</p>
                    <Badge
                      variant={getStatusColor(activity.status)}
                      className={`text-xs font-medium ${
                        activity.status === 'completed'
                          ? 'bg-success/20 text-success border-success/30'
                          : activity.status === 'pending'
                          ? 'bg-warning/20 text-warning border-warning/30'
                          : 'bg-destructive/20 text-destructive border-destructive/30'
                      }`}
                    >
                      {t(activity.status)}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{activity.description}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(activity.timestamp, {
                      addSuffix: true,
                      locale: isArabic ? ar : enUS
                    })}
                    {activity.user && (
                      <span className="mx-1 text-primary">•</span>
                    )}
                    {activity.user && (
                      <span className="font-medium">{activity.user}</span>
                    )}
                  </p>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}