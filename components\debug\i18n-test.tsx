'use client'

import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function I18nTest() {
  const { t, i18n } = useTranslation()

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>i18n Debug Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <strong>Current Language:</strong> {i18n.language}
        </div>
        <div>
          <strong>Is i18n Ready:</strong> {i18n.isInitialized ? 'Yes' : 'No'}
        </div>
        <div>
          <strong>Welcome Translation:</strong> "{t('welcome')}"
        </div>
        <div>
          <strong>Dashboard Translation:</strong> "{t('dashboard')}"
        </div>
        <div>
          <strong>Login Translation:</strong> "{t('login')}"
        </div>
        <div>
          <strong>Available Languages:</strong> {Object.keys(i18n.options.resources || {}).join(', ')}
        </div>
        <div>
          <strong>Fallback Language:</strong> {JSON.stringify(i18n.options.fallbackLng)}
        </div>
        <div>
          <strong>Raw Translation Test:</strong>
          <pre className="bg-gray-100 p-2 rounded text-sm">
            {JSON.stringify({
              welcome_ar: t('welcome'),
              welcome_en: i18n.getResource('en', 'translation', 'welcome'),
              current_lang: i18n.language
            }, null, 2)}
          </pre>
        </div>
      </CardContent>
    </Card>
  )
}
