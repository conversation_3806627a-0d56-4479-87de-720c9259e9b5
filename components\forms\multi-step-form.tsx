'use client'

import React, { createContext, useContext, useState, ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, Save, Check } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface Step {
  id: string
  title: string
  description?: string
  component: ReactNode
  isValid?: boolean
  isOptional?: boolean
}

interface MultiStepFormContextType {
  currentStep: number
  steps: Step[]
  formData: Record<string, any>
  isStepValid: (stepIndex: number) => boolean
  goToStep: (stepIndex: number) => void
  nextStep: () => void
  previousStep: () => void
  updateFormData: (data: Partial<Record<string, any>>) => void
  isFirstStep: boolean
  isLastStep: boolean
  canProceed: boolean
}

const MultiStepFormContext = createContext<MultiStepFormContextType | undefined>(undefined)

export const useMultiStepForm = () => {
  const context = useContext(MultiStepFormContext)
  if (!context) {
    throw new Error('useMultiStepForm must be used within a MultiStepFormProvider')
  }
  return context
}

interface MultiStepFormProviderProps {
  children: ReactNode
  steps: Step[]
  initialData?: Record<string, any>
  onStepChange?: (stepIndex: number) => void
  onDataChange?: (data: Record<string, any>) => void
}

export const MultiStepFormProvider: React.FC<MultiStepFormProviderProps> = ({
  children,
  steps,
  initialData = {},
  onStepChange,
  onDataChange,
}) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<Record<string, any>>(initialData)

  const isStepValid = (stepIndex: number): boolean => {
    const step = steps[stepIndex]
    return step?.isValid !== false
  }

  const goToStep = (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStep(stepIndex)
      onStepChange?.(stepIndex)
    }
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1 && canProceed) {
      const newStep = currentStep + 1
      setCurrentStep(newStep)
      onStepChange?.(newStep)
    }
  }

  const previousStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1
      setCurrentStep(newStep)
      onStepChange?.(newStep)
    }
  }

  const updateFormData = (data: Partial<Record<string, any>>) => {
    const newData = { ...formData, ...data }
    setFormData(newData)
    onDataChange?.(newData)
  }

  const isFirstStep = currentStep === 0
  const isLastStep = currentStep === steps.length - 1
  const canProceed = isStepValid(currentStep)

  const contextValue: MultiStepFormContextType = {
    currentStep,
    steps,
    formData,
    isStepValid,
    goToStep,
    nextStep,
    previousStep,
    updateFormData,
    isFirstStep,
    isLastStep,
    canProceed,
  }

  return (
    <MultiStepFormContext.Provider value={contextValue}>
      {children}
    </MultiStepFormContext.Provider>
  )
}

interface StepIndicatorProps {
  className?: string
}

export const StepIndicator: React.FC<StepIndicatorProps> = ({ className }) => {
  const { currentStep, steps, isStepValid, goToStep } = useMultiStepForm()
  const { t, i18n } = useTranslation()

  const progressPercentage = ((currentStep + 1) / steps.length) * 100

  return (
    <div className={cn('space-y-4', className)}>
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>{t('step')} {currentStep + 1} {t('of')} {steps.length}</span>
          <span>{Math.round(progressPercentage)}% {t('complete')}</span>
        </div>
        <Progress value={progressPercentage} className="h-2" />
      </div>

      {/* Step Indicators */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isActive = index === currentStep
          const isCompleted = index < currentStep
          const isValid = isStepValid(index)
          const isClickable = index <= currentStep || isCompleted

          return (
            <div
              key={step.id}
              className="flex flex-col items-center space-y-2 flex-1"
            >
              <button
                onClick={() => isClickable && goToStep(index)}
                disabled={!isClickable}
                className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all',
                  'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                  {
                    'bg-primary text-primary-foreground': isActive && isValid,
                    'bg-destructive text-destructive-foreground': isActive && !isValid,
                    'bg-success text-success-foreground': isCompleted && isValid,
                    'bg-muted text-muted-foreground': !isActive && !isCompleted,
                    'cursor-pointer hover:bg-primary/80': isClickable,
                    'cursor-not-allowed opacity-50': !isClickable,
                  }
                )}
              >
                {isCompleted && isValid ? (
                  <Check className="h-4 w-4" />
                ) : (
                  index + 1
                )}
              </button>
              <div className="text-center">
                <div className={cn(
                  'text-xs font-medium',
                  {
                    'text-primary': isActive,
                    'text-muted-foreground': !isActive,
                  }
                )}>
                  {t(step.title)}
                </div>
                {step.isOptional && (
                  <Badge variant="outline" className="text-xs mt-1">
                    {t('optional')}
                  </Badge>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

interface StepContentProps {
  className?: string
}

export const StepContent: React.FC<StepContentProps> = ({ className }) => {
  const { currentStep, steps } = useMultiStepForm()
  const currentStepData = steps[currentStep]

  if (!currentStepData) {
    return null
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div className="space-y-2">
        <h2 className="text-2xl font-bold">{currentStepData.title}</h2>
        {currentStepData.description && (
          <p className="text-muted-foreground">{currentStepData.description}</p>
        )}
      </div>
      <div>{currentStepData.component}</div>
    </div>
  )
}

interface StepNavigationProps {
  onSaveDraft?: () => void
  onSubmit?: () => void
  className?: string
  showSaveDraft?: boolean
}

export const StepNavigation: React.FC<StepNavigationProps> = ({
  onSaveDraft,
  onSubmit,
  className,
  showSaveDraft = true,
}) => {
  const { 
    isFirstStep, 
    isLastStep, 
    canProceed, 
    nextStep, 
    previousStep 
  } = useMultiStepForm()
  const { t } = useTranslation()

  return (
    <div className={cn('flex items-center justify-between pt-6', className)}>
      <div className="flex items-center gap-2">
        {!isFirstStep && (
          <Button variant="outline" onClick={previousStep}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('previous')}
          </Button>
        )}
      </div>

      <div className="flex items-center gap-2">
        {showSaveDraft && onSaveDraft && (
          <Button variant="ghost" onClick={onSaveDraft}>
            <Save className="mr-2 h-4 w-4" />
            {t('save_draft')}
          </Button>
        )}
        
        {isLastStep ? (
          <Button 
            onClick={onSubmit} 
            disabled={!canProceed}
            className="min-w-24"
          >
            {t('submit')}
          </Button>
        ) : (
          <Button 
            onClick={nextStep} 
            disabled={!canProceed}
            className="min-w-24"
          >
            {t('next')}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}

interface MultiStepFormProps {
  steps: Step[]
  initialData?: Record<string, any>
  onStepChange?: (stepIndex: number) => void
  onDataChange?: (data: Record<string, any>) => void
  onSaveDraft?: () => void
  onSubmit?: () => void
  showSaveDraft?: boolean
  className?: string
}

export const MultiStepForm: React.FC<MultiStepFormProps> = ({
  steps,
  initialData,
  onStepChange,
  onDataChange,
  onSaveDraft,
  onSubmit,
  showSaveDraft = true,
  className,
}) => {
  return (
    <MultiStepFormProvider
      steps={steps}
      initialData={initialData}
      onStepChange={onStepChange}
      onDataChange={onDataChange}
    >
      <Card className={cn('w-full max-w-4xl mx-auto', className)}>
        <CardHeader>
          <StepIndicator />
        </CardHeader>
        <CardContent>
          <StepContent />
          <StepNavigation
            onSaveDraft={onSaveDraft}
            onSubmit={onSubmit}
            showSaveDraft={showSaveDraft}
          />
        </CardContent>
      </Card>
    </MultiStepFormProvider>
  )
}
