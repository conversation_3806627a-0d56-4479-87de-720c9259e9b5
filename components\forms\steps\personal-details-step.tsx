'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useTranslation } from 'react-i18next'
import { CalendarIcon, User, CreditCard, Calendar } from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'

import { personalDetailsSchema, PersonalDetailsFormData } from '@/lib/validation/beneficiary-registration'
import { useMultiStepForm } from '@/components/forms/multi-step-form'
import { cn } from '@/lib/utils'

interface PersonalDetailsStepProps {
  onValidationChange?: (isValid: boolean) => void
}

export const PersonalDetailsStep: React.FC<PersonalDetailsStepProps> = ({
  onValidationChange,
}) => {
  const { t, i18n } = useTranslation()
  const { formData, updateFormData } = useMultiStepForm()

  const form = useForm({
    resolver: zodResolver(personalDetailsSchema),
    defaultValues: {
      fullNameAr: formData.personalDetails?.fullNameAr || '',
      fullNameEn: formData.personalDetails?.fullNameEn || '',
      nationalId: formData.personalDetails?.nationalId || '',
      dateOfBirth: formData.personalDetails?.dateOfBirth || new Date(),
      gender: formData.personalDetails?.gender || 'male',
      maritalStatus: formData.personalDetails?.maritalStatus || 'single',
    },
    mode: 'onChange',
  })

  const { watch, formState: { isValid, errors } } = form

  // Watch for form changes and update parent form data
  React.useEffect(() => {
    const subscription = watch((value) => {
      updateFormData({
        personalDetails: value as PersonalDetailsFormData,
      })
      onValidationChange?.(isValid)
    })
    return () => subscription.unsubscribe()
  }, [watch, updateFormData, isValid])

  // Notify parent of initial validation state
  React.useEffect(() => {
    onValidationChange?.(isValid)
  }, [isValid])

  const isRTL = i18n.language === 'ar'

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('personal_information')}
          </CardTitle>
          <CardDescription>
            {t('personal_details_description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <div className="grid gap-6">
              {/* Full Name Section */}
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="fullNameAr"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        {t('name_arabic')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('enter_name_arabic')}
                          {...field}
                          className={cn(
                            'text-right',
                            errors.fullNameAr && 'border-destructive'
                          )}
                          dir="rtl"
                        />
                      </FormControl>
                      <FormDescription>
                        {t('name_arabic_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="fullNameEn"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        {t('name_english')}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('enter_name_english')}
                          {...field}
                          className={cn(
                            errors.fullNameEn && 'border-destructive'
                          )}
                          dir="ltr"
                        />
                      </FormControl>
                      <FormDescription>
                        {t('name_english_description')}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* National ID */}
              <FormField
                control={form.control}
                name="nationalId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      {t('national_id')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('enter_national_id')}
                        {...field}
                        className={cn(
                          'font-mono',
                          errors.nationalId && 'border-destructive'
                        )}
                        maxLength={10}
                        dir="ltr"
                      />
                    </FormControl>
                    <FormDescription>
                      {t('national_id_description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Date of Birth */}
              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {t('date_of_birth')}
                    </FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'w-full pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground',
                              errors.dateOfBirth && 'border-destructive',
                              isRTL && 'text-right'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP', {
                                locale: isRTL ? ar : undefined,
                              })
                            ) : (
                              <span>{t('pick_date')}</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date('1900-01-01')
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      {t('date_of_birth_description')}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Gender and Marital Status */}
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('gender')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className={cn(
                            errors.gender && 'border-destructive'
                          )}>
                            <SelectValue placeholder={t('select_gender')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="male">{t('male')}</SelectItem>
                          <SelectItem value="female">{t('female')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maritalStatus"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('marital_status')}</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className={cn(
                            errors.maritalStatus && 'border-destructive'
                          )}>
                            <SelectValue placeholder={t('select_marital_status')} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="single">{t('single')}</SelectItem>
                          <SelectItem value="married">{t('married')}</SelectItem>
                          <SelectItem value="divorced">{t('divorced')}</SelectItem>
                          <SelectItem value="widowed">{t('widowed')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  )
}
