# Zakat Bank System – User Stories (English Translation)

*Translated from Arabic on August 17, 2025*

# Arab

## US-SP1-01

- As a zakat application provider, I would like to log in using the traffic name and password through the national documentation system "Documentation" so that the interface of the applicants for the Zakat program successfully entered the Zakat program
## US-SP1-02

- As a zakat application provider, I would like to enter personal data until all the required personal data is completed and the profile of accreditation is completed.
## US-SP1-03

- As a zakat application provider, I would like to send the profile to accredit until the profile is approved and the account activates the user in order to be able to submit assistance applications
## US-SP1-04

- As an employee of the Reception Division, I would like to review the new profile request / or request to amend the profile data until it is approved or rejected with the reason for the rejection
## US-SP1-05

- As a zakat application provider, I would like to apply for the personal data amendment until the applicant to amend the personal data is sent
## US-SP1-06

- As an employee of the Reception Division, I would like to create the profile by entering the applicant's data, which cannot submit a request to create a profile through the system until the account is created for the applicant
## US-SP1-07

- As the system official, I want to determine the compulsory and optional data for requesting a new personal file so that the application process is managed in an organized and tight manner
## US-SP1-08

- As a zakat applicant, I would like to create a request for assistance by choosing the required type
## US-SP1-09

- The Zakat system is to ensure the validity of the attached documents requesting assistance until the application is sent to the receptionist employee for the review
## US-SP1-10

- As the system official, I want to identify the required documents for each type of aid in a flexible way until the applicant is allowed to attach these documents
## US-SP1-11

- As the system official, I want to define the types of aid flexible so that the system is adjustable at the time of the system through the system management screen
## US-SP1-12

- As the system official, I want to link the types of aid, for example, with nationality, so that the same national holders are allowed to apply for a specific assistance
## US-SP1-13

- As an employee of the Reception Division, I would like to reach the applications (request to create a personal file / amend a personal file / request for help) through the daily tasks screen so that each employee has a special screen for the tasks sent to him
## The second stage / internal treatment portal

## US-SP1-15

- As an employee of the Reception Division, I would like to reach the applications (request to create a personal file / amend a personal file / request for help) through the daily tasks screen so that each employee has a special screen for the tasks sent to him
## US-SP1-16

- As an employee of the Reception Division, I would like to review the requests (request to create a personal file / amend a personal file / request for help) through the daily task screen until the details of the required help and attachments are displayed according to the type of application
## US-SP1-17

- The system creates a case report through the questions and data entered automatically until the researcher is facilitated to review the report and amend the recommendation
## US-SP1-18

- As a researcher, I want to review the attached aid applications and documents and write recommendations until the decision to request assistance is taken
## US-SP1-19

- As a banking expert, I would like to review the applications for aid, attached documents, the researcher's recommendations, and make a decision until the decision to request assistance is taken
## US-SP1-20

- As the head of the department, I would like to review aid applications and recommendations from the banking expert and amend the decision until the decision to request assistance is taken
## US-SP1-21

- As the head of the department, I want to return aid requests to the banking expert until the decision to request assistance is taken
## US-SP1-22

- As the director of the administration, I would like to see the attached aid requests, documents, expert recommendations and decision -making until the decision to request assistance is taken
## US-SP1-23

- The minister, I want to view the aid requests that make up the value of the support more than 50 thousand, the expert recommendations, the director of the administration's director and the decision to take until the decision to request assistance is taken
## US-SP1-24

- As a zakat employee, I want to access the profile screen so that all the personal files of users are found
## US-SP1-25

- As a zakat employee, I want to access the request screen so that all aid requests are viewed
## US-SP1-27

- As the system official, I want to determine the mechanism of distribution of applications in order to distribute applications to employees automatically and systematically
## US-SP1-28

- As the system official, I want to link the types of aid to the functional role of the expert or the field of expert
## US-SP1-29

- As the system official, I want to manage the business rules for requests so that the requests are addressed through the workflow, for example, the roles of bank experts and their competencies, the value of aid and the workflow for each of them

