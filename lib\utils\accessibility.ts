/**
 * Accessibility utilities for WCAG 2.1 AA compliance
 * Ensures proper screen reader support, keyboard navigation, and inclusive design
 */

import { TFunction } from 'i18next'

// ARIA live region types
export type LiveRegionType = 'polite' | 'assertive' | 'off'

// Focus management utilities
export class FocusManager {
  private static focusHistory: HTMLElement[] = []

  /**
   * Save current focus for later restoration
   */
  static saveFocus(): void {
    const activeElement = document.activeElement as HTMLElement
    if (activeElement && activeElement !== document.body) {
      this.focusHistory.push(activeElement)
    }
  }

  /**
   * Restore previously saved focus
   */
  static restoreFocus(): void {
    const lastFocused = this.focusHistory.pop()
    if (lastFocused && document.contains(lastFocused)) {
      lastFocused.focus()
    }
  }

  /**
   * Focus the first focusable element in a container
   */
  static focusFirst(container: HTMLElement): void {
    const focusable = this.getFocusableElements(container)
    if (focusable.length > 0) {
      focusable[0].focus()
    }
  }

  /**
   * Get all focusable elements in a container
   */
  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ')

    return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[]
  }

  /**
   * Trap focus within a container (for modals, dialogs)
   */
  static trapFocus(container: HTMLElement): () => void {
    const focusableElements = this.getFocusableElements(container)
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement?.focus()
        }
      }
    }

    container.addEventListener('keydown', handleTabKey)

    // Return cleanup function
    return () => {
      container.removeEventListener('keydown', handleTabKey)
    }
  }
}

// Live region announcements for screen readers
export class LiveRegion {
  private static regions: Map<string, HTMLElement> = new Map()

  /**
   * Create or get a live region for announcements
   */
  static getRegion(type: LiveRegionType = 'polite'): HTMLElement {
    const regionId = `live-region-${type}`
    
    if (!this.regions.has(regionId)) {
      const region = document.createElement('div')
      region.id = regionId
      region.setAttribute('aria-live', type)
      region.setAttribute('aria-atomic', 'true')
      region.className = 'sr-only' // Screen reader only
      region.style.cssText = `
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
      `
      
      document.body.appendChild(region)
      this.regions.set(regionId, region)
    }

    return this.regions.get(regionId)!
  }

  /**
   * Announce a message to screen readers
   */
  static announce(message: string, type: LiveRegionType = 'polite'): void {
    const region = this.getRegion(type)
    
    // Clear and set new message
    region.textContent = ''
    setTimeout(() => {
      region.textContent = message
    }, 100)
  }

  /**
   * Clear all announcements
   */
  static clear(): void {
    this.regions.forEach(region => {
      region.textContent = ''
    })
  }
}

// Form accessibility helpers
export const FormAccessibility = {
  /**
   * Generate accessible form field IDs and ARIA attributes
   */
  generateFieldAttributes(fieldName: string, options: {
    label?: string
    description?: string
    error?: string
    required?: boolean
  } = {}) {
    const fieldId = `field-${fieldName}`
    const labelId = `label-${fieldName}`
    const descriptionId = options.description ? `desc-${fieldName}` : undefined
    const errorId = options.error ? `error-${fieldName}` : undefined

    const describedBy = [descriptionId, errorId].filter(Boolean).join(' ')

    return {
      field: {
        id: fieldId,
        'aria-labelledby': labelId,
        'aria-describedby': describedBy || undefined,
        'aria-required': options.required || undefined,
        'aria-invalid': options.error ? 'true' : undefined,
      },
      label: {
        id: labelId,
        htmlFor: fieldId,
      },
      description: descriptionId ? {
        id: descriptionId,
        'aria-hidden': 'false',
      } : undefined,
      error: errorId ? {
        id: errorId,
        role: 'alert',
        'aria-live': 'polite' as const,
      } : undefined,
    }
  },

  /**
   * Announce form validation errors
   */
  announceValidationErrors(errors: Record<string, string>, t: TFunction): void {
    const errorCount = Object.keys(errors).length
    if (errorCount === 0) return

    const message = errorCount === 1 
      ? t('form_has_error', { count: errorCount })
      : t('form_has_errors', { count: errorCount })

    LiveRegion.announce(message, 'assertive')
  },

  /**
   * Announce successful form submission
   */
  announceSuccess(message: string): void {
    LiveRegion.announce(message, 'polite')
  },

  /**
   * Focus first error field
   */
  focusFirstError(errors: Record<string, string>): void {
    const firstErrorField = Object.keys(errors)[0]
    if (firstErrorField) {
      const element = document.getElementById(`field-${firstErrorField}`)
      if (element) {
        element.focus()
        element.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    }
  },
}

// Keyboard navigation helpers
export const KeyboardNavigation = {
  /**
   * Handle arrow key navigation in a list
   */
  handleArrowNavigation(
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onIndexChange: (newIndex: number) => void
  ): void {
    if (!['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) return

    event.preventDefault()
    let newIndex = currentIndex

    switch (event.key) {
      case 'ArrowUp':
        newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1
        break
      case 'ArrowDown':
        newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0
        break
      case 'Home':
        newIndex = 0
        break
      case 'End':
        newIndex = items.length - 1
        break
    }

    onIndexChange(newIndex)
    items[newIndex]?.focus()
  },

  /**
   * Handle escape key to close modals/dropdowns
   */
  handleEscape(event: KeyboardEvent, onEscape: () => void): void {
    if (event.key === 'Escape') {
      event.preventDefault()
      onEscape()
    }
  },

  /**
   * Handle enter/space key for custom interactive elements
   */
  handleActivation(event: KeyboardEvent, onActivate: () => void): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      onActivate()
    }
  },
}

// Color contrast utilities
export const ColorContrast = {
  /**
   * Calculate color contrast ratio
   */
  calculateContrast(color1: string, color2: string): number {
    const getLuminance = (color: string): number => {
      // Convert hex to RGB
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16) / 255
      const g = parseInt(hex.substr(2, 2), 16) / 255
      const b = parseInt(hex.substr(4, 2), 16) / 255

      // Calculate relative luminance
      const toLinear = (c: number) => c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
      return 0.2126 * toLinear(r) + 0.7152 * toLinear(g) + 0.0722 * toLinear(b)
    }

    const lum1 = getLuminance(color1)
    const lum2 = getLuminance(color2)
    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)

    return (brightest + 0.05) / (darkest + 0.05)
  },

  /**
   * Check if color combination meets WCAG AA standards
   */
  meetsWCAGAA(foreground: string, background: string, isLargeText = false): boolean {
    const contrast = this.calculateContrast(foreground, background)
    return isLargeText ? contrast >= 3 : contrast >= 4.5
  },

  /**
   * Check if color combination meets WCAG AAA standards
   */
  meetsWCAGAAA(foreground: string, background: string, isLargeText = false): boolean {
    const contrast = this.calculateContrast(foreground, background)
    return isLargeText ? contrast >= 4.5 : contrast >= 7
  },
}

// Screen reader utilities
export const ScreenReader = {
  /**
   * Create screen reader only text
   */
  createSROnlyText(text: string): HTMLSpanElement {
    const span = document.createElement('span')
    span.textContent = text
    span.className = 'sr-only'
    span.style.cssText = `
      position: absolute !important;
      width: 1px !important;
      height: 1px !important;
      padding: 0 !important;
      margin: -1px !important;
      overflow: hidden !important;
      clip: rect(0, 0, 0, 0) !important;
      white-space: nowrap !important;
      border: 0 !important;
    `
    return span
  },

  /**
   * Add screen reader context to form steps
   */
  announceStepChange(currentStep: number, totalSteps: number, stepTitle: string, t: TFunction): void {
    const message = t('step_announcement', {
      current: currentStep + 1,
      total: totalSteps,
      title: stepTitle,
    })
    LiveRegion.announce(message, 'polite')
  },

  /**
   * Announce progress updates
   */
  announceProgress(percentage: number, t: TFunction): void {
    const message = t('progress_announcement', { percentage: Math.round(percentage) })
    LiveRegion.announce(message, 'polite')
  },
}

// All utilities are already exported above
