import { z } from 'zod'
import { TFunction } from 'i18next'

// Enhanced validation utilities for the beneficiary registration form

export interface ValidationError {
  field: string
  message: string
  code: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationError[]
}

// Duplicate detection utilities
export interface DuplicateCheckResult {
  isDuplicate: boolean
  duplicateFields: string[]
  existingBeneficiaryId?: string
  confidence: number
}

// Mock database for duplicate checking (in real app, this would be an API call)
const mockBeneficiaries = [
  {
    id: 'ben-001',
    nationalId: '1234567890',
    fullNameAr: 'أحمد محمد علي',
    fullNameEn: '<PERSON>',
    phoneNumber: '+966501234567',
    email: '<EMAIL>',
  },
  {
    id: 'ben-002',
    nationalId: '0987654321',
    fullNameAr: 'فاطمة عبدالله',
    fullNameEn: '<PERSON><PERSON>',
    phoneNumber: '+966509876543',
    email: '<EMAIL>',
  },
]

/**
 * Check for duplicate beneficiaries based on key identifying information
 */
export const checkForDuplicates = async (formData: {
  nationalId?: string
  fullNameAr?: string
  fullNameEn?: string
  phoneNumber?: string
  email?: string
}): Promise<DuplicateCheckResult> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))

  const duplicateFields: string[] = []
  let existingBeneficiaryId: string | undefined
  let confidence = 0

  for (const beneficiary of mockBeneficiaries) {
    // Check National ID (highest priority)
    if (formData.nationalId && beneficiary.nationalId === formData.nationalId) {
      duplicateFields.push('nationalId')
      existingBeneficiaryId = beneficiary.id
      confidence = 100
      break
    }

    // Check phone number (high priority)
    if (formData.phoneNumber && beneficiary.phoneNumber === formData.phoneNumber) {
      duplicateFields.push('phoneNumber')
      existingBeneficiaryId = beneficiary.id
      confidence = Math.max(confidence, 90)
    }

    // Check email (medium priority)
    if (formData.email && beneficiary.email === formData.email) {
      duplicateFields.push('email')
      existingBeneficiaryId = beneficiary.id
      confidence = Math.max(confidence, 70)
    }

    // Check name similarity (lower priority)
    if (formData.fullNameAr && beneficiary.fullNameAr) {
      const similarity = calculateNameSimilarity(formData.fullNameAr, beneficiary.fullNameAr)
      if (similarity > 0.8) {
        duplicateFields.push('fullNameAr')
        existingBeneficiaryId = beneficiary.id
        confidence = Math.max(confidence, 60)
      }
    }

    if (formData.fullNameEn && beneficiary.fullNameEn) {
      const similarity = calculateNameSimilarity(formData.fullNameEn, beneficiary.fullNameEn)
      if (similarity > 0.8) {
        duplicateFields.push('fullNameEn')
        existingBeneficiaryId = beneficiary.id
        confidence = Math.max(confidence, 60)
      }
    }
  }

  return {
    isDuplicate: duplicateFields.length > 0,
    duplicateFields,
    existingBeneficiaryId,
    confidence,
  }
}

/**
 * Calculate similarity between two names using Levenshtein distance
 */
const calculateNameSimilarity = (name1: string, name2: string): number => {
  const normalize = (str: string) => str.toLowerCase().trim().replace(/\s+/g, ' ')
  const n1 = normalize(name1)
  const n2 = normalize(name2)

  if (n1 === n2) return 1

  const matrix: number[][] = []
  const len1 = n1.length
  const len2 = n2.length

  for (let i = 0; i <= len1; i++) {
    matrix[i] = [i]
  }

  for (let j = 0; j <= len2; j++) {
    matrix[0][j] = j
  }

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = n1[i - 1] === n2[j - 1] ? 0 : 1
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost
      )
    }
  }

  const distance = matrix[len1][len2]
  const maxLength = Math.max(len1, len2)
  return maxLength === 0 ? 1 : 1 - distance / maxLength
}

/**
 * Enhanced validation with custom error messages
 */
export const validateFormStep = (
  stepName: string,
  data: any,
  t: TFunction
): ValidationResult => {
  const errors: ValidationError[] = []
  const warnings: ValidationError[] = []

  try {
    // Import validation schemas dynamically to avoid circular dependencies
    const { validateStep } = require('../validation/beneficiary-registration')
    const result = validateStep(stepName, data)

    if (!result.success) {
      result.error.errors.forEach((error: z.ZodIssue) => {
        const field = error.path.join('.')
        const code = error.code
        let message = error.message

        // Translate error messages
        try {
          if (code === 'too_small' && 'minimum' in error) {
            message = t('min_length', { min: (error as any).minimum })
          } else if (code === 'too_big' && 'maximum' in error) {
            message = t('max_length', { max: (error as any).maximum })
          } else if (code === 'invalid_type') {
            message = t('field_required')
          } else {
            // Try to translate the message, fallback to original
            const translatedMessage = t(error.message)
            message = translatedMessage !== error.message ? translatedMessage : error.message
          }
        } catch (translationError) {
          message = error.message
        }

        errors.push({
          field,
          message,
          code,
        })
      })
    }

    // Add custom business logic validations
    if (stepName === 'personalDetails') {
      // Check if age is reasonable
      if (data.dateOfBirth) {
        const age = new Date().getFullYear() - new Date(data.dateOfBirth).getFullYear()
        if (age < 18) {
          warnings.push({
            field: 'dateOfBirth',
            message: t('age_below_18_warning'),
            code: 'age_warning',
          })
        }
        if (age > 100) {
          warnings.push({
            field: 'dateOfBirth',
            message: t('age_above_100_warning'),
            code: 'age_warning',
          })
        }
      }
    }

    if (stepName === 'eligibilityCriteria') {
      // Check if family size and dependents are consistent
      if (data.familySize && data.dependents && data.dependents >= data.familySize) {
        warnings.push({
          field: 'dependents',
          message: t('dependents_family_size_warning'),
          code: 'consistency_warning',
        })
      }

      // Check if income is very high for Zakat eligibility
      if (data.monthlyIncome && data.monthlyIncome > 20000) {
        warnings.push({
          field: 'monthlyIncome',
          message: t('high_income_warning'),
          code: 'eligibility_warning',
        })
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    }
  } catch (error) {
    console.error('Validation error:', error)
    return {
      isValid: false,
      errors: [{
        field: 'general',
        message: t('validation_error'),
        code: 'validation_error',
      }],
      warnings: [],
    }
  }
}

/**
 * Real-time field validation
 */
export const validateField = (
  fieldName: string,
  value: any,
  stepName: string,
  t: TFunction
): ValidationError | null => {
  try {
    // Create a minimal object with just this field for validation
    const testData = { [fieldName]: value }
    const result = validateFormStep(stepName, testData, t)
    
    const fieldError = result.errors.find(error => error.field === fieldName)
    return fieldError || null
  } catch (error) {
    console.error('Field validation error:', error)
    return {
      field: fieldName,
      message: t('validation_error'),
      code: 'validation_error',
    }
  }
}

/**
 * Format validation errors for display
 */
export const formatValidationErrors = (
  errors: ValidationError[],
  t: TFunction
): Record<string, string> => {
  const formatted: Record<string, string> = {}
  
  errors.forEach(error => {
    formatted[error.field] = error.message
  })
  
  return formatted
}

/**
 * Check if all required fields are filled
 */
export const checkRequiredFields = (
  stepName: string,
  data: any,
  t: TFunction
): ValidationError[] => {
  const errors: ValidationError[] = []
  
  const requiredFields: Record<string, string[]> = {
    personalDetails: ['fullNameAr', 'fullNameEn', 'nationalId', 'dateOfBirth', 'gender', 'maritalStatus'],
    contactInformation: ['phoneNumber', 'address', 'city', 'region'],
    eligibilityCriteria: ['primaryCategory', 'zakatCategories', 'familySize', 'dependents'],
    documentation: ['documents'],
    reviewSubmit: ['termsAccepted', 'dataAccuracyConfirmed', 'privacyPolicyAccepted'],
  }
  
  const fields = requiredFields[stepName] || []
  
  fields.forEach(field => {
    const value = data[field]
    if (value === undefined || value === null || value === '' || 
        (Array.isArray(value) && value.length === 0)) {
      errors.push({
        field,
        message: t('field_required'),
        code: 'required',
      })
    }
  })
  
  return errors
}

/**
 * Get validation summary for all steps
 */
export const getValidationSummary = (
  formData: any,
  t: TFunction
): Record<string, ValidationResult> => {
  const steps = ['personalDetails', 'contactInformation', 'eligibilityCriteria', 'documentation', 'reviewSubmit']
  const summary: Record<string, ValidationResult> = {}
  
  steps.forEach(step => {
    const stepData = formData[step]
    summary[step] = validateFormStep(step, stepData, t)
  })
  
  return summary
}
