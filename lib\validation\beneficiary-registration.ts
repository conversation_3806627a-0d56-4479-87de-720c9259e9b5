import { z } from 'zod'
import { ZakatCategory } from '@/lib/types'

// Saudi National ID validation regex
const SAUDI_NATIONAL_ID_REGEX = /^[12]\d{9}$/

// Saudi phone number validation regex
const SAUDI_PHONE_REGEX = /^(\+966|966|0)?[5][0-9]{8}$/

// Email validation (more permissive than default)
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

// Arabic text validation (allows Arabic letters, spaces, and common punctuation)
const ARABIC_TEXT_REGEX = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\-\.]+$/

// English text validation (allows English letters, spaces, and common punctuation)
const ENGLISH_TEXT_REGEX = /^[a-zA-Z\s\-\.]+$/

// Step 1: Personal Details Schema
export const personalDetailsSchema = z.object({
  fullNameAr: z
    .string()
    .min(2, 'min_length')
    .max(100, 'max_length')
    .regex(ARABIC_TEXT_REGEX, 'invalid_arabic_name'),
  
  fullNameEn: z
    .string()
    .min(2, 'min_length')
    .max(100, 'max_length')
    .regex(ENGLISH_TEXT_REGEX, 'invalid_english_name'),
  
  nationalId: z
    .string()
    .regex(SAUDI_NATIONAL_ID_REGEX, 'invalid_national_id'),
  
  dateOfBirth: z
    .date()
    .refine((date) => {
      const today = new Date()
      const age = today.getFullYear() - date.getFullYear()
      return age >= 0 && age <= 120
    }, 'invalid_age')
    .refine((date) => {
      return date <= new Date()
    }, 'future_date_not_allowed'),
  
  gender: z.enum(['male', 'female'], {
    message: 'field_required',
  }),

  maritalStatus: z.enum(['single', 'married', 'divorced', 'widowed'], {
    message: 'field_required',
  }),
})

// Step 2: Contact Information Schema
export const contactInformationSchema = z.object({
  phoneNumber: z
    .string()
    .regex(SAUDI_PHONE_REGEX, 'invalid_phone'),
  
  email: z
    .string()
    .email('invalid_email')
    .optional()
    .or(z.literal('')),
  
  address: z
    .string()
    .min(10, 'min_length')
    .max(200, 'max_length'),
  
  city: z
    .string()
    .min(2, 'min_length')
    .max(50, 'max_length'),
  
  region: z
    .string()
    .min(2, 'min_length')
    .max(50, 'max_length'),
  
  postalCode: z
    .string()
    .regex(/^\d{5}$/, 'invalid_postal_code')
    .optional()
    .or(z.literal('')),
})

// Step 3: Eligibility Criteria Schema
export const eligibilityCriteriaSchema = z.object({
  primaryCategory: z.enum([
    'fuqara',
    'masakin',
    'amilin',
    'muallafah',
    'riqab',
    'gharimin',
    'fisabilillah',
    'ibnus_sabil'
  ] as const, {
    message: 'field_required',
  }),
  
  zakatCategories: z
    .array(z.enum([
      'fuqara',
      'masakin',
      'amilin',
      'muallafah',
      'riqab',
      'gharimin',
      'fisabilillah',
      'ibnus_sabil'
    ] as const))
    .min(1, 'select_at_least_one_category')
    .refine((categories) => {
      // Primary category should be included in selected categories
      return true // This will be validated in the form component
    }),
  
  familySize: z
    .number()
    .min(1, 'min_family_size')
    .max(20, 'max_family_size'),
  
  dependents: z
    .number()
    .min(0, 'min_dependents')
    .max(19, 'max_dependents'),
  
  monthlyIncome: z
    .number()
    .min(0, 'invalid_income')
    .max(50000, 'max_income')
    .optional(),
  
  hasSpecialNeeds: z.boolean().default(false),
  
  specialNeedsDescription: z
    .string()
    .max(500, 'max_length')
    .optional()
    .or(z.literal('')),
})

// Step 4: Documentation Upload Schema
export const documentationSchema = z.object({
  documents: z
    .array(z.object({
      id: z.string(),
      type: z.enum(['national_id', 'income_certificate', 'family_card', 'medical_report', 'other']),
      name: z.string(),
      file: z.any(), // File object
      size: z.number(),
      mimeType: z.string(),
    }))
    .min(1, 'upload_at_least_one_document'),
  
  nationalIdUploaded: z.boolean().refine((val) => val === true, 'national_id_required'),
  
  additionalNotes: z
    .string()
    .max(1000, 'max_length')
    .optional()
    .or(z.literal('')),
})

// Step 5: Review and Submit Schema
export const reviewSubmitSchema = z.object({
  termsAccepted: z.boolean().refine((val) => val === true, 'terms_must_be_accepted'),
  
  dataAccuracyConfirmed: z.boolean().refine((val) => val === true, 'data_accuracy_must_be_confirmed'),
  
  privacyPolicyAccepted: z.boolean().refine((val) => val === true, 'privacy_policy_must_be_accepted'),
})

// Complete Beneficiary Registration Schema
export const beneficiaryRegistrationSchema = z.object({
  personalDetails: personalDetailsSchema,
  contactInformation: contactInformationSchema,
  eligibilityCriteria: eligibilityCriteriaSchema,
  documentation: documentationSchema,
  reviewSubmit: reviewSubmitSchema,
})

// Type definitions
export type PersonalDetailsFormData = z.infer<typeof personalDetailsSchema>
export type ContactInformationFormData = z.infer<typeof contactInformationSchema>
export type EligibilityCriteriaFormData = z.infer<typeof eligibilityCriteriaSchema>
export type DocumentationFormData = z.infer<typeof documentationSchema>
export type ReviewSubmitFormData = z.infer<typeof reviewSubmitSchema>
export type BeneficiaryRegistrationFormData = z.infer<typeof beneficiaryRegistrationSchema>

// Helper function to validate individual steps
export const validateStep = (stepName: string, data: any) => {
  switch (stepName) {
    case 'personalDetails':
      return personalDetailsSchema.safeParse(data)
    case 'contactInformation':
      return contactInformationSchema.safeParse(data)
    case 'eligibilityCriteria':
      return eligibilityCriteriaSchema.safeParse(data)
    case 'documentation':
      return documentationSchema.safeParse(data)
    case 'reviewSubmit':
      return reviewSubmitSchema.safeParse(data)
    default:
      throw new Error(`Unknown step: ${stepName}`)
  }
}

// Helper function to get validation errors in a user-friendly format
export const getValidationErrors = (result: any) => {
  if (result.success) return {}

  const errors: Record<string, string> = {}
  result.error.errors.forEach((error: any) => {
    const path = error.path.join('.')
    errors[path] = error.message
  })

  return errors
}

// Helper function to check if a step is valid
export const isStepValid = (stepName: string, data: any): boolean => {
  const result = validateStep(stepName, data)
  return result.success
}

// Default form data
export const defaultFormData: Partial<BeneficiaryRegistrationFormData> = {
  personalDetails: {
    fullNameAr: '',
    fullNameEn: '',
    nationalId: '',
    dateOfBirth: new Date(),
    gender: 'male',
    maritalStatus: 'single',
  },
  contactInformation: {
    phoneNumber: '',
    email: '',
    address: '',
    city: '',
    region: '',
    postalCode: '',
  },
  eligibilityCriteria: {
    primaryCategory: 'fuqara',
    zakatCategories: ['fuqara'],
    familySize: 1,
    dependents: 0,
    monthlyIncome: 0,
    hasSpecialNeeds: false,
    specialNeedsDescription: '',
  },
  documentation: {
    documents: [],
    nationalIdUploaded: false,
    additionalNotes: '',
  },
  reviewSubmit: {
    termsAccepted: false,
    dataAccuracyConfirmed: false,
    privacyPolicyAccepted: false,
  },
}
